* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    padding: 20px;
    max-width: 800px;
    width: 100%;
}

.game-header {
    text-align: center;
    margin-bottom: 20px;
}

.game-header h1 {
    color: #4a5568;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-content {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.game-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 200px;
}

.score-section, .level-section, .lines-section, .next-piece-section, .controls-section {
    background: #f7fafc;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.score-section h3, .level-section h3, .lines-section h3, .next-piece-section h3, .controls-section h3 {
    color: #2d3748;
    margin-bottom: 10px;
    font-size: 1.1em;
}

#score, #level, #lines {
    font-size: 2em;
    font-weight: bold;
    color: #4299e1;
    text-align: center;
}

#nextCanvas {
    border: 2px solid #e2e8f0;
    border-radius: 5px;
    background: #fff;
    display: block;
    margin: 0 auto;
}

.controls {
    font-size: 0.9em;
    line-height: 1.6;
}

.controls div {
    margin-bottom: 5px;
    color: #4a5568;
}

.game-board {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

#gameCanvas {
    border: 3px solid #2d3748;
    border-radius: 10px;
    background: #1a202c;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.game-over, .pause-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.5);
}

.game-over h2, .pause-overlay h2 {
    margin-bottom: 15px;
    font-size: 2em;
}

.game-over p, .pause-overlay p {
    margin-bottom: 20px;
    font-size: 1.2em;
}

#restartBtn {
    background: #4299e1;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1.1em;
    cursor: pointer;
    transition: background 0.3s ease;
}

#restartBtn:hover {
    background: #3182ce;
}

.hidden {
    display: none !important;
}

@media (max-width: 768px) {
    .game-content {
        flex-direction: column;
        align-items: center;
    }
    
    .game-info {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        min-width: auto;
    }
    
    .score-section, .level-section, .lines-section {
        flex: 1;
        min-width: 120px;
    }
    
    .next-piece-section, .controls-section {
        flex: 1;
        min-width: 200px;
    }
    
    #gameCanvas {
        width: 250px;
        height: 500px;
    }
}
