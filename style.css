@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-bg: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    --secondary-bg: linear-gradient(145deg, #1e1e3f 0%, #2a2a5a 100%);
    --accent-color: #00d4ff;
    --accent-glow: #00d4ff;
    --text-primary: #ffffff;
    --text-secondary: #b8c5d6;
    --card-bg: rgba(30, 30, 63, 0.8);
    --card-border: rgba(0, 212, 255, 0.3);
    --success-color: #00ff88;
    --warning-color: #ffaa00;
    --danger-color: #ff4757;
}

body {
    font-family: 'Exo 2', sans-serif;
    background: var(--primary-bg);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--text-primary);
    overflow: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 150, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 255, 136, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.game-container {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--card-border);
    border-radius: 24px;
    box-shadow:
        0 32px 64px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    padding: 32px;
    max-width: 1000px;
    width: 100%;
    position: relative;
}

.game-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 24px;
    padding: 1px;
    background: linear-gradient(45deg, var(--accent-color), transparent, var(--accent-color));
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0.5;
    pointer-events: none;
}

.game-header {
    text-align: center;
    margin-bottom: 32px;
    position: relative;
}

.game-header h1 {
    font-family: 'Orbitron', monospace;
    font-weight: 900;
    font-size: 3.5em;
    background: linear-gradient(45deg, var(--accent-color), #ff0080, var(--success-color));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
    letter-spacing: 2px;
    position: relative;
}

.game-header h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
    border-radius: 2px;
}

.game-content {
    display: flex;
    gap: 32px;
    justify-content: center;
    align-items: flex-start;
}

.game-info {
    display: flex;
    flex-direction: column;
    gap: 24px;
    min-width: 280px;
}

.score-section, .level-section, .lines-section, .next-piece-section, .controls-section {
    background: linear-gradient(145deg, rgba(30, 30, 63, 0.9), rgba(42, 42, 90, 0.7));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 16px;
    padding: 20px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.score-section::before, .level-section::before, .lines-section::before,
.next-piece-section::before, .controls-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-color), var(--success-color), var(--accent-color));
    opacity: 0.8;
}

.score-section h3, .level-section h3, .lines-section h3, .next-piece-section h3, .controls-section h3 {
    color: var(--text-primary);
    margin-bottom: 12px;
    font-size: 1.2em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Orbitron', monospace;
}

#score, #level, #lines {
    font-size: 2.5em;
    font-weight: 700;
    font-family: 'Orbitron', monospace;
    text-align: center;
    background: linear-gradient(45deg, var(--accent-color), var(--success-color));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
    margin: 8px 0;
}

#nextCanvas {
    border: 2px solid rgba(0, 212, 255, 0.3);
    border-radius: 12px;
    background: linear-gradient(145deg, #0a0a1a, #1a1a2e);
    display: block;
    margin: 0 auto;
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.controls {
    font-size: 1em;
    line-height: 1.8;
}

.controls div {
    margin-bottom: 8px;
    color: var(--text-secondary);
    padding: 8px 12px;
    background: rgba(0, 212, 255, 0.05);
    border-radius: 8px;
    border-left: 3px solid var(--accent-color);
    font-family: 'Orbitron', monospace;
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.controls div:hover {
    background: rgba(0, 212, 255, 0.1);
    transform: translateX(4px);
}

.game-board {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

#gameCanvas {
    border: 3px solid rgba(0, 212, 255, 0.4);
    border-radius: 16px;
    background: linear-gradient(145deg, #0a0a1a 0%, #1a1a2e 100%);
    box-shadow:
        0 0 40px rgba(0, 212, 255, 0.3),
        0 16px 32px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
}

#gameCanvas::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, var(--accent-color), transparent, var(--success-color), transparent, var(--accent-color));
    border-radius: 16px;
    z-index: -1;
    opacity: 0.6;
    animation: borderGlow 3s ease-in-out infinite alternate;
}

@keyframes borderGlow {
    0% { opacity: 0.6; }
    100% { opacity: 1; }
}

.game-over, .pause-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(145deg, rgba(10, 10, 26, 0.95), rgba(26, 26, 46, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 212, 255, 0.3);
    color: var(--text-primary);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.6),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    min-width: 300px;
}

.game-over h2, .pause-overlay h2 {
    margin-bottom: 20px;
    font-size: 2.5em;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    background: linear-gradient(45deg, var(--accent-color), var(--success-color));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.game-over p, .pause-overlay p {
    margin-bottom: 24px;
    font-size: 1.3em;
    color: var(--text-secondary);
    font-weight: 300;
}

#restartBtn {
    background: linear-gradient(45deg, var(--accent-color), var(--success-color));
    color: var(--text-primary);
    border: none;
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 1.2em;
    font-weight: 600;
    font-family: 'Orbitron', monospace;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow:
        0 4px 16px rgba(0, 212, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

#restartBtn:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 24px rgba(0, 212, 255, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

#restartBtn:active {
    transform: translateY(0);
}

.hidden {
    display: none !important;
}

/* 添加一些动画效果 */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.score-section, .level-section, .lines-section, .next-piece-section, .controls-section {
    animation: slideIn 0.6s ease-out;
}

.score-section { animation-delay: 0.1s; }
.level-section { animation-delay: 0.2s; }
.lines-section { animation-delay: 0.3s; }
.next-piece-section { animation-delay: 0.4s; }
.controls-section { animation-delay: 0.5s; }

/* 响应式设计 */
@media (max-width: 1024px) {
    .game-container {
        padding: 24px;
        margin: 20px;
    }

    .game-header h1 {
        font-size: 2.8em;
    }
}

@media (max-width: 768px) {
    body {
        overflow-y: auto;
        align-items: flex-start;
        padding: 20px 0;
    }

    .game-container {
        margin: 0 16px;
        padding: 20px;
    }

    .game-header h1 {
        font-size: 2.2em;
    }

    .game-content {
        flex-direction: column;
        align-items: center;
        gap: 24px;
    }

    .game-info {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        min-width: auto;
        width: 100%;
        max-width: 400px;
    }

    .score-section, .level-section, .lines-section {
        flex: 1;
        min-width: 100px;
        margin: 4px;
    }

    .next-piece-section, .controls-section {
        flex: 1;
        min-width: 180px;
        margin: 4px;
    }

    #gameCanvas {
        width: 250px;
        height: 500px;
    }

    #score, #level, #lines {
        font-size: 1.8em;
    }
}

@media (max-width: 480px) {
    .game-container {
        margin: 0 8px;
        padding: 16px;
    }

    .game-header h1 {
        font-size: 1.8em;
    }

    .game-info {
        max-width: 300px;
    }

    #gameCanvas {
        width: 200px;
        height: 400px;
    }
}
