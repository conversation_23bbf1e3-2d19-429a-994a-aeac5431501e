// 俄罗斯方块游戏
class Tetris {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.nextCanvas = document.getElementById('nextCanvas');
        this.nextCtx = this.nextCanvas.getContext('2d');
        
        // 游戏配置
        this.BOARD_WIDTH = 10;
        this.BOARD_HEIGHT = 20;
        this.BLOCK_SIZE = 30;
        
        // 游戏状态
        this.board = this.createBoard();
        this.currentPiece = null;
        this.nextPiece = null;
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.gameRunning = false;
        this.isPaused = false;
        this.dropTime = 0;
        this.dropInterval = 1000; // 1秒

        // 动画状态
        this.clearingLines = [];
        this.clearAnimation = 0;
        this.isClearing = false;

        // 粒子系统
        this.particles = [];
        
        // 方块类型定义 - 使用现代化的渐变色彩
        this.pieces = [
            // I型
            {
                shape: [
                    [1, 1, 1, 1]
                ],
                color: '#00d4ff',
                gradient: ['#00d4ff', '#0099cc']
            },
            // O型
            {
                shape: [
                    [1, 1],
                    [1, 1]
                ],
                color: '#ffaa00',
                gradient: ['#ffaa00', '#cc8800']
            },
            // T型
            {
                shape: [
                    [0, 1, 0],
                    [1, 1, 1]
                ],
                color: '#ff0080',
                gradient: ['#ff0080', '#cc0066']
            },
            // S型
            {
                shape: [
                    [0, 1, 1],
                    [1, 1, 0]
                ],
                color: '#00ff88',
                gradient: ['#00ff88', '#00cc66']
            },
            // Z型
            {
                shape: [
                    [1, 1, 0],
                    [0, 1, 1]
                ],
                color: '#ff4757',
                gradient: ['#ff4757', '#cc3644']
            },
            // J型
            {
                shape: [
                    [1, 0, 0],
                    [1, 1, 1]
                ],
                color: '#5352ed',
                gradient: ['#5352ed', '#4241be']
            },
            // L型
            {
                shape: [
                    [0, 0, 1],
                    [1, 1, 1]
                ],
                color: '#ff9ff3',
                gradient: ['#ff9ff3', '#cc7fc2']
            }
        ];
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.newGame();
    }
    
    createBoard() {
        return Array(this.BOARD_HEIGHT).fill().map(() => Array(this.BOARD_WIDTH).fill(0));
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
        document.getElementById('restartBtn').addEventListener('click', () => this.newGame());
    }
    
    newGame() {
        this.board = this.createBoard();
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.gameRunning = true;
        this.isPaused = false;
        this.dropInterval = 1000;
        this.dropTime = Date.now(); // 初始化下降时间

        this.currentPiece = this.createPiece();
        this.nextPiece = this.createPiece();

        this.updateDisplay();
        this.hideGameOver();
        this.hidePause();

        this.gameLoop();
    }
    
    createPiece() {
        const pieceType = this.pieces[Math.floor(Math.random() * this.pieces.length)];
        return {
            shape: pieceType.shape.map(row => [...row]),
            color: pieceType.color,
            gradient: pieceType.gradient,
            x: Math.floor(this.BOARD_WIDTH / 2) - Math.floor(pieceType.shape[0].length / 2),
            y: 0
        };
    }
    
    handleKeyPress(e) {
        if (!this.gameRunning) return;

        switch(e.code) {
            case 'ArrowLeft':
                e.preventDefault();
                this.movePiece(-1, 0);
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.movePiece(1, 0);
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.movePiece(0, 1);
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.rotatePiece();
                break;
            case 'Space':
                e.preventDefault();
                if (this.isPaused) {
                    this.togglePause();
                } else {
                    this.hardDrop();
                }
                break;
            case 'KeyP':
                e.preventDefault();
                this.togglePause();
                break;
        }
    }
    
    movePiece(dx, dy) {
        if (this.isPaused) return;
        
        const newX = this.currentPiece.x + dx;
        const newY = this.currentPiece.y + dy;
        
        if (this.isValidPosition(this.currentPiece.shape, newX, newY)) {
            this.currentPiece.x = newX;
            this.currentPiece.y = newY;
            this.draw();
        } else if (dy > 0) {
            // 方块无法继续下降，固定到游戏板
            this.placePiece();
        }
    }
    
    rotatePiece() {
        if (this.isPaused) return;

        const rotated = this.rotateMatrix(this.currentPiece.shape);
        if (this.isValidPosition(rotated, this.currentPiece.x, this.currentPiece.y)) {
            this.currentPiece.shape = rotated;
            this.draw();
        }
    }

    hardDrop() {
        if (this.isPaused) return;

        let dropDistance = 0;
        while (this.isValidPosition(this.currentPiece.shape, this.currentPiece.x, this.currentPiece.y + dropDistance + 1)) {
            dropDistance++;
        }

        this.currentPiece.y += dropDistance;
        this.score += dropDistance * 2; // 硬降落额外得分
        this.placePiece();
        this.draw();
    }
    
    rotateMatrix(matrix) {
        const rows = matrix.length;
        const cols = matrix[0].length;
        const rotated = Array(cols).fill().map(() => Array(rows).fill(0));
        
        for (let i = 0; i < rows; i++) {
            for (let j = 0; j < cols; j++) {
                rotated[j][rows - 1 - i] = matrix[i][j];
            }
        }
        
        return rotated;
    }
    
    isValidPosition(shape, x, y) {
        for (let i = 0; i < shape.length; i++) {
            for (let j = 0; j < shape[i].length; j++) {
                if (shape[i][j]) {
                    const newX = x + j;
                    const newY = y + i;
                    
                    if (newX < 0 || newX >= this.BOARD_WIDTH || 
                        newY >= this.BOARD_HEIGHT || 
                        (newY >= 0 && this.board[newY][newX])) {
                        return false;
                    }
                }
            }
        }
        return true;
    }
    
    placePiece() {
        // 将当前方块放置到游戏板上
        for (let i = 0; i < this.currentPiece.shape.length; i++) {
            for (let j = 0; j < this.currentPiece.shape[i].length; j++) {
                if (this.currentPiece.shape[i][j]) {
                    const x = this.currentPiece.x + j;
                    const y = this.currentPiece.y + i;
                    if (y >= 0) {
                        this.board[y][x] = this.currentPiece.color;
                    }
                }
            }
        }

        // 检查是否有完整的行
        this.clearLines();

        // 生成新方块
        this.currentPiece = this.nextPiece;
        this.nextPiece = this.createPiece();

        // 检查游戏是否结束
        if (!this.isValidPosition(this.currentPiece.shape, this.currentPiece.x, this.currentPiece.y)) {
            this.gameOver();
        }

        this.updateDisplay();
    }

    clearLines() {
        const linesToClear = [];

        for (let y = this.BOARD_HEIGHT - 1; y >= 0; y--) {
            if (this.board[y].every(cell => cell !== 0)) {
                linesToClear.push(y);
            }
        }

        if (linesToClear.length > 0) {
            // 创建粒子效果
            this.createLineParticles(linesToClear);

            // 开始清除动画
            this.clearingLines = linesToClear;
            this.clearAnimation = 0;
            this.isClearing = true;

            // 立即更新分数和等级
            this.lines += linesToClear.length;
            const scoreMap = [0, 100, 300, 500, 800];
            this.score += scoreMap[linesToClear.length] * this.level;

            // 每消除10行提升一个等级
            this.level = Math.floor(this.lines / 10) + 1;

            // 随着等级提升，下降速度加快
            this.dropInterval = Math.max(100, 1000 - (this.level - 1) * 100);

            this.updateDisplay();
        }
    }

    updateClearAnimation() {
        if (!this.isClearing) return;

        this.clearAnimation += 0.1;

        if (this.clearAnimation >= 1) {
            // 动画完成，实际移除行
            for (let i = this.clearingLines.length - 1; i >= 0; i--) {
                this.board.splice(this.clearingLines[i], 1);
                this.board.unshift(Array(this.BOARD_WIDTH).fill(0));
            }

            this.clearingLines = [];
            this.clearAnimation = 0;
            this.isClearing = false;
        }
    }

    createLineParticles(lines) {
        for (const lineY of lines) {
            for (let x = 0; x < this.BOARD_WIDTH; x++) {
                if (this.board[lineY][x]) {
                    // 为每个方块创建多个粒子
                    for (let i = 0; i < 8; i++) {
                        this.particles.push({
                            x: x * this.BLOCK_SIZE + this.BLOCK_SIZE / 2,
                            y: lineY * this.BLOCK_SIZE + this.BLOCK_SIZE / 2,
                            vx: (Math.random() - 0.5) * 8,
                            vy: (Math.random() - 0.5) * 8 - 2,
                            life: 1.0,
                            decay: 0.02,
                            color: this.board[lineY][x],
                            size: Math.random() * 4 + 2
                        });
                    }
                }
            }
        }
    }

    updateParticles() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];

            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.vy += 0.3; // 重力
            particle.life -= particle.decay;

            if (particle.life <= 0) {
                this.particles.splice(i, 1);
            }
        }
    }

    drawParticles() {
        for (const particle of this.particles) {
            this.ctx.save();
            this.ctx.globalAlpha = particle.life;
            this.ctx.fillStyle = particle.color;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        }
    }

    togglePause() {
        this.isPaused = !this.isPaused;
        if (this.isPaused) {
            this.showPause();
        } else {
            this.hidePause();
        }
    }

    gameOver() {
        this.gameRunning = false;
        this.showGameOver();
    }

    showGameOver() {
        document.getElementById('finalScore').textContent = this.score;
        document.getElementById('gameOver').classList.remove('hidden');
    }

    hideGameOver() {
        document.getElementById('gameOver').classList.add('hidden');
    }

    showPause() {
        document.getElementById('pauseOverlay').classList.remove('hidden');
    }

    hidePause() {
        document.getElementById('pauseOverlay').classList.add('hidden');
    }

    updateDisplay() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('level').textContent = this.level;
        document.getElementById('lines').textContent = this.lines;
        this.drawNextPiece();
    }

    draw() {
        // 清空画布并绘制背景渐变
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#0a0a1a');
        gradient.addColorStop(1, '#1a1a2e');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制游戏板
        this.drawBoard();

        // 绘制当前方块
        if (this.currentPiece) {
            this.drawPiece(this.currentPiece, this.ctx);
        }

        // 绘制网格线
        this.drawGrid();

        // 绘制粒子效果
        this.drawParticles();
    }

    drawBoard() {
        for (let y = 0; y < this.BOARD_HEIGHT; y++) {
            for (let x = 0; x < this.BOARD_WIDTH; x++) {
                if (this.board[y][x]) {
                    // 检查是否是正在清除的行
                    const isClearing = this.clearingLines.includes(y);
                    if (isClearing) {
                        this.drawClearingBlock(x, y, this.board[y][x], this.ctx);
                    } else {
                        this.drawBlock(x, y, this.board[y][x], this.ctx);
                    }
                }
            }
        }
    }

    drawClearingBlock(x, y, color, context) {
        const blockX = x * this.BLOCK_SIZE;
        const blockY = y * this.BLOCK_SIZE;

        // 闪烁效果
        const alpha = Math.sin(this.clearAnimation * Math.PI * 4) * 0.5 + 0.5;

        // 创建闪烁的渐变
        const gradient = context.createLinearGradient(
            blockX, blockY,
            blockX + this.BLOCK_SIZE, blockY + this.BLOCK_SIZE
        );

        gradient.addColorStop(0, `rgba(255, 255, 255, ${alpha})`);
        gradient.addColorStop(1, `rgba(0, 212, 255, ${alpha})`);

        context.fillStyle = gradient;
        context.fillRect(blockX, blockY, this.BLOCK_SIZE, this.BLOCK_SIZE);

        // 添加发光边框
        context.strokeStyle = `rgba(0, 212, 255, ${alpha})`;
        context.lineWidth = 2;
        context.strokeRect(blockX, blockY, this.BLOCK_SIZE, this.BLOCK_SIZE);
    }

    drawBlock(x, y, color, context, isGhost = false) {
        const blockX = x * this.BLOCK_SIZE;
        const blockY = y * this.BLOCK_SIZE;

        // 创建渐变效果
        const gradient = context.createLinearGradient(
            blockX, blockY,
            blockX + this.BLOCK_SIZE, blockY + this.BLOCK_SIZE
        );

        if (typeof color === 'string') {
            // 单色方块（已放置的方块）
            gradient.addColorStop(0, color);
            gradient.addColorStop(1, this.darkenColor(color, 0.3));
        } else {
            // 渐变方块（当前方块）
            gradient.addColorStop(0, color[0]);
            gradient.addColorStop(1, color[1]);
        }

        context.fillStyle = isGhost ? `${color}40` : gradient;
        context.fillRect(blockX, blockY, this.BLOCK_SIZE, this.BLOCK_SIZE);

        // 添加高光效果
        if (!isGhost) {
            const highlight = context.createLinearGradient(
                blockX, blockY,
                blockX + this.BLOCK_SIZE * 0.3, blockY + this.BLOCK_SIZE * 0.3
            );
            highlight.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
            highlight.addColorStop(1, 'rgba(255, 255, 255, 0)');
            context.fillStyle = highlight;
            context.fillRect(blockX, blockY, this.BLOCK_SIZE, this.BLOCK_SIZE);
        }

        // 添加边框
        context.strokeStyle = isGhost ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.5)';
        context.lineWidth = 1;
        context.strokeRect(blockX, blockY, this.BLOCK_SIZE, this.BLOCK_SIZE);

        // 内边框高光
        if (!isGhost) {
            context.strokeStyle = 'rgba(255, 255, 255, 0.2)';
            context.lineWidth = 1;
            context.strokeRect(blockX + 1, blockY + 1, this.BLOCK_SIZE - 2, this.BLOCK_SIZE - 2);
        }
    }

    darkenColor(color, factor) {
        const hex = color.replace('#', '');
        const r = Math.max(0, parseInt(hex.substr(0, 2), 16) * (1 - factor));
        const g = Math.max(0, parseInt(hex.substr(2, 2), 16) * (1 - factor));
        const b = Math.max(0, parseInt(hex.substr(4, 2), 16) * (1 - factor));
        return `rgb(${Math.floor(r)}, ${Math.floor(g)}, ${Math.floor(b)})`;
    }

    drawPiece(piece, context) {
        for (let i = 0; i < piece.shape.length; i++) {
            for (let j = 0; j < piece.shape[i].length; j++) {
                if (piece.shape[i][j]) {
                    const x = piece.x + j;
                    const y = piece.y + i;

                    // 只绘制在游戏板范围内的方块
                    if (x >= 0 && x < this.BOARD_WIDTH && y >= 0 && y < this.BOARD_HEIGHT) {
                        this.drawBlock(x, y, piece.gradient || piece.color, context);
                    }
                }
            }
        }
    }

    drawGrid() {
        this.ctx.strokeStyle = 'rgba(0, 212, 255, 0.1)';
        this.ctx.lineWidth = 0.5;

        // 绘制垂直线
        for (let x = 0; x <= this.BOARD_WIDTH; x++) {
            this.ctx.beginPath();
            this.ctx.moveTo(x * this.BLOCK_SIZE, 0);
            this.ctx.lineTo(x * this.BLOCK_SIZE, this.canvas.height);
            this.ctx.stroke();
        }

        // 绘制水平线
        for (let y = 0; y <= this.BOARD_HEIGHT; y++) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y * this.BLOCK_SIZE);
            this.ctx.lineTo(this.canvas.width, y * this.BLOCK_SIZE);
            this.ctx.stroke();
        }
    }

    drawNextPiece() {
        // 清空下一个方块的画布并绘制背景渐变
        const gradient = this.nextCtx.createLinearGradient(0, 0, 0, this.nextCanvas.height);
        gradient.addColorStop(0, '#0a0a1a');
        gradient.addColorStop(1, '#1a1a2e');
        this.nextCtx.fillStyle = gradient;
        this.nextCtx.fillRect(0, 0, this.nextCanvas.width, this.nextCanvas.height);

        if (this.nextPiece) {
            const blockSize = 20;
            const offsetX = (this.nextCanvas.width - this.nextPiece.shape[0].length * blockSize) / 2;
            const offsetY = (this.nextCanvas.height - this.nextPiece.shape.length * blockSize) / 2;

            for (let i = 0; i < this.nextPiece.shape.length; i++) {
                for (let j = 0; j < this.nextPiece.shape[i].length; j++) {
                    if (this.nextPiece.shape[i][j]) {
                        const x = offsetX + j * blockSize;
                        const y = offsetY + i * blockSize;

                        // 创建小方块的渐变
                        const blockGradient = this.nextCtx.createLinearGradient(x, y, x + blockSize, y + blockSize);
                        if (this.nextPiece.gradient) {
                            blockGradient.addColorStop(0, this.nextPiece.gradient[0]);
                            blockGradient.addColorStop(1, this.nextPiece.gradient[1]);
                        } else {
                            blockGradient.addColorStop(0, this.nextPiece.color);
                            blockGradient.addColorStop(1, this.darkenColor(this.nextPiece.color, 0.3));
                        }

                        this.nextCtx.fillStyle = blockGradient;
                        this.nextCtx.fillRect(x, y, blockSize, blockSize);

                        // 添加高光
                        const highlight = this.nextCtx.createLinearGradient(x, y, x + blockSize * 0.3, y + blockSize * 0.3);
                        highlight.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
                        highlight.addColorStop(1, 'rgba(255, 255, 255, 0)');
                        this.nextCtx.fillStyle = highlight;
                        this.nextCtx.fillRect(x, y, blockSize, blockSize);

                        // 添加边框
                        this.nextCtx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
                        this.nextCtx.lineWidth = 1;
                        this.nextCtx.strokeRect(x, y, blockSize, blockSize);

                        // 内边框高光
                        this.nextCtx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
                        this.nextCtx.strokeRect(x + 1, y + 1, blockSize - 2, blockSize - 2);
                    }
                }
            }
        }
    }

    gameLoop() {
        if (!this.gameRunning) return;

        const currentTime = Date.now();

        // 更新清除动画
        this.updateClearAnimation();

        // 更新粒子
        this.updateParticles();

        if (!this.isPaused && !this.isClearing && currentTime - this.dropTime > this.dropInterval) {
            this.movePiece(0, 1);
            this.dropTime = currentTime;
        }

        this.draw();

        requestAnimationFrame(() => this.gameLoop());
    }
}

// 启动游戏
document.addEventListener('DOMContentLoaded', () => {
    new Tetris();
});
