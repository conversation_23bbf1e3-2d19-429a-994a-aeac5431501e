// 俄罗斯方块游戏
class Tetris {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.nextCanvas = document.getElementById('nextCanvas');
        this.nextCtx = this.nextCanvas.getContext('2d');
        
        // 游戏配置
        this.BOARD_WIDTH = 10;
        this.BOARD_HEIGHT = 20;
        this.BLOCK_SIZE = 30;
        
        // 游戏状态
        this.board = this.createBoard();
        this.currentPiece = null;
        this.nextPiece = null;
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.gameRunning = false;
        this.isPaused = false;
        this.dropTime = 0;
        this.dropInterval = 1000; // 1秒
        
        // 方块类型定义
        this.pieces = [
            // I型
            {
                shape: [
                    [1, 1, 1, 1]
                ],
                color: '#00f5ff'
            },
            // O型
            {
                shape: [
                    [1, 1],
                    [1, 1]
                ],
                color: '#ffff00'
            },
            // T型
            {
                shape: [
                    [0, 1, 0],
                    [1, 1, 1]
                ],
                color: '#800080'
            },
            // S型
            {
                shape: [
                    [0, 1, 1],
                    [1, 1, 0]
                ],
                color: '#00ff00'
            },
            // Z型
            {
                shape: [
                    [1, 1, 0],
                    [0, 1, 1]
                ],
                color: '#ff0000'
            },
            // J型
            {
                shape: [
                    [1, 0, 0],
                    [1, 1, 1]
                ],
                color: '#0000ff'
            },
            // L型
            {
                shape: [
                    [0, 0, 1],
                    [1, 1, 1]
                ],
                color: '#ffa500'
            }
        ];
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.newGame();
    }
    
    createBoard() {
        return Array(this.BOARD_HEIGHT).fill().map(() => Array(this.BOARD_WIDTH).fill(0));
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));
        document.getElementById('restartBtn').addEventListener('click', () => this.newGame());
    }
    
    newGame() {
        this.board = this.createBoard();
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.gameRunning = true;
        this.isPaused = false;
        this.dropInterval = 1000;
        this.dropTime = Date.now(); // 初始化下降时间

        this.currentPiece = this.createPiece();
        this.nextPiece = this.createPiece();

        this.updateDisplay();
        this.hideGameOver();
        this.hidePause();

        this.gameLoop();
    }
    
    createPiece() {
        const pieceType = this.pieces[Math.floor(Math.random() * this.pieces.length)];
        return {
            shape: pieceType.shape.map(row => [...row]),
            color: pieceType.color,
            x: Math.floor(this.BOARD_WIDTH / 2) - Math.floor(pieceType.shape[0].length / 2),
            y: 0
        };
    }
    
    handleKeyPress(e) {
        if (!this.gameRunning) return;

        switch(e.code) {
            case 'ArrowLeft':
                e.preventDefault();
                this.movePiece(-1, 0);
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.movePiece(1, 0);
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.movePiece(0, 1);
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.rotatePiece();
                break;
            case 'Space':
                e.preventDefault();
                if (this.isPaused) {
                    this.togglePause();
                } else {
                    this.hardDrop();
                }
                break;
            case 'KeyP':
                e.preventDefault();
                this.togglePause();
                break;
        }
    }
    
    movePiece(dx, dy) {
        if (this.isPaused) return;
        
        const newX = this.currentPiece.x + dx;
        const newY = this.currentPiece.y + dy;
        
        if (this.isValidPosition(this.currentPiece.shape, newX, newY)) {
            this.currentPiece.x = newX;
            this.currentPiece.y = newY;
            this.draw();
        } else if (dy > 0) {
            // 方块无法继续下降，固定到游戏板
            this.placePiece();
        }
    }
    
    rotatePiece() {
        if (this.isPaused) return;

        const rotated = this.rotateMatrix(this.currentPiece.shape);
        if (this.isValidPosition(rotated, this.currentPiece.x, this.currentPiece.y)) {
            this.currentPiece.shape = rotated;
            this.draw();
        }
    }

    hardDrop() {
        if (this.isPaused) return;

        let dropDistance = 0;
        while (this.isValidPosition(this.currentPiece.shape, this.currentPiece.x, this.currentPiece.y + dropDistance + 1)) {
            dropDistance++;
        }

        this.currentPiece.y += dropDistance;
        this.score += dropDistance * 2; // 硬降落额外得分
        this.placePiece();
        this.draw();
    }
    
    rotateMatrix(matrix) {
        const rows = matrix.length;
        const cols = matrix[0].length;
        const rotated = Array(cols).fill().map(() => Array(rows).fill(0));
        
        for (let i = 0; i < rows; i++) {
            for (let j = 0; j < cols; j++) {
                rotated[j][rows - 1 - i] = matrix[i][j];
            }
        }
        
        return rotated;
    }
    
    isValidPosition(shape, x, y) {
        for (let i = 0; i < shape.length; i++) {
            for (let j = 0; j < shape[i].length; j++) {
                if (shape[i][j]) {
                    const newX = x + j;
                    const newY = y + i;
                    
                    if (newX < 0 || newX >= this.BOARD_WIDTH || 
                        newY >= this.BOARD_HEIGHT || 
                        (newY >= 0 && this.board[newY][newX])) {
                        return false;
                    }
                }
            }
        }
        return true;
    }
    
    placePiece() {
        // 将当前方块放置到游戏板上
        for (let i = 0; i < this.currentPiece.shape.length; i++) {
            for (let j = 0; j < this.currentPiece.shape[i].length; j++) {
                if (this.currentPiece.shape[i][j]) {
                    const x = this.currentPiece.x + j;
                    const y = this.currentPiece.y + i;
                    if (y >= 0) {
                        this.board[y][x] = this.currentPiece.color;
                    }
                }
            }
        }

        // 检查是否有完整的行
        this.clearLines();

        // 生成新方块
        this.currentPiece = this.nextPiece;
        this.nextPiece = this.createPiece();

        // 检查游戏是否结束
        if (!this.isValidPosition(this.currentPiece.shape, this.currentPiece.x, this.currentPiece.y)) {
            this.gameOver();
        }

        this.updateDisplay();
    }

    clearLines() {
        let linesCleared = 0;

        for (let y = this.BOARD_HEIGHT - 1; y >= 0; y--) {
            if (this.board[y].every(cell => cell !== 0)) {
                // 移除完整的行
                this.board.splice(y, 1);
                // 在顶部添加新的空行
                this.board.unshift(Array(this.BOARD_WIDTH).fill(0));
                linesCleared++;
                y++; // 重新检查当前行
            }
        }

        if (linesCleared > 0) {
            this.lines += linesCleared;
            // 计分规则：单行100分，双行300分，三行500分，四行800分
            const scoreMap = [0, 100, 300, 500, 800];
            this.score += scoreMap[linesCleared] * this.level;

            // 每消除10行提升一个等级
            this.level = Math.floor(this.lines / 10) + 1;

            // 随着等级提升，下降速度加快
            this.dropInterval = Math.max(100, 1000 - (this.level - 1) * 100);
        }
    }

    togglePause() {
        this.isPaused = !this.isPaused;
        if (this.isPaused) {
            this.showPause();
        } else {
            this.hidePause();
        }
    }

    gameOver() {
        this.gameRunning = false;
        this.showGameOver();
    }

    showGameOver() {
        document.getElementById('finalScore').textContent = this.score;
        document.getElementById('gameOver').classList.remove('hidden');
    }

    hideGameOver() {
        document.getElementById('gameOver').classList.add('hidden');
    }

    showPause() {
        document.getElementById('pauseOverlay').classList.remove('hidden');
    }

    hidePause() {
        document.getElementById('pauseOverlay').classList.add('hidden');
    }

    updateDisplay() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('level').textContent = this.level;
        document.getElementById('lines').textContent = this.lines;
        this.drawNextPiece();
    }

    draw() {
        // 清空画布
        this.ctx.fillStyle = '#1a202c';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制游戏板
        this.drawBoard();

        // 绘制当前方块
        if (this.currentPiece) {
            this.drawPiece(this.currentPiece, this.ctx);
        }

        // 绘制网格线
        this.drawGrid();
    }

    drawBoard() {
        for (let y = 0; y < this.BOARD_HEIGHT; y++) {
            for (let x = 0; x < this.BOARD_WIDTH; x++) {
                if (this.board[y][x]) {
                    this.ctx.fillStyle = this.board[y][x];
                    this.ctx.fillRect(
                        x * this.BLOCK_SIZE,
                        y * this.BLOCK_SIZE,
                        this.BLOCK_SIZE,
                        this.BLOCK_SIZE
                    );

                    // 添加边框
                    this.ctx.strokeStyle = '#000';
                    this.ctx.lineWidth = 1;
                    this.ctx.strokeRect(
                        x * this.BLOCK_SIZE,
                        y * this.BLOCK_SIZE,
                        this.BLOCK_SIZE,
                        this.BLOCK_SIZE
                    );
                }
            }
        }
    }

    drawPiece(piece, context) {
        context.fillStyle = piece.color;

        for (let i = 0; i < piece.shape.length; i++) {
            for (let j = 0; j < piece.shape[i].length; j++) {
                if (piece.shape[i][j]) {
                    const x = (piece.x + j) * this.BLOCK_SIZE;
                    const y = (piece.y + i) * this.BLOCK_SIZE;

                    context.fillRect(x, y, this.BLOCK_SIZE, this.BLOCK_SIZE);

                    // 添加边框
                    context.strokeStyle = '#000';
                    context.lineWidth = 1;
                    context.strokeRect(x, y, this.BLOCK_SIZE, this.BLOCK_SIZE);
                }
            }
        }
    }

    drawGrid() {
        this.ctx.strokeStyle = '#2d3748';
        this.ctx.lineWidth = 1;

        // 绘制垂直线
        for (let x = 0; x <= this.BOARD_WIDTH; x++) {
            this.ctx.beginPath();
            this.ctx.moveTo(x * this.BLOCK_SIZE, 0);
            this.ctx.lineTo(x * this.BLOCK_SIZE, this.canvas.height);
            this.ctx.stroke();
        }

        // 绘制水平线
        for (let y = 0; y <= this.BOARD_HEIGHT; y++) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y * this.BLOCK_SIZE);
            this.ctx.lineTo(this.canvas.width, y * this.BLOCK_SIZE);
            this.ctx.stroke();
        }
    }

    drawNextPiece() {
        // 清空下一个方块的画布
        this.nextCtx.fillStyle = '#fff';
        this.nextCtx.fillRect(0, 0, this.nextCanvas.width, this.nextCanvas.height);

        if (this.nextPiece) {
            const blockSize = 20;
            const offsetX = (this.nextCanvas.width - this.nextPiece.shape[0].length * blockSize) / 2;
            const offsetY = (this.nextCanvas.height - this.nextPiece.shape.length * blockSize) / 2;

            this.nextCtx.fillStyle = this.nextPiece.color;

            for (let i = 0; i < this.nextPiece.shape.length; i++) {
                for (let j = 0; j < this.nextPiece.shape[i].length; j++) {
                    if (this.nextPiece.shape[i][j]) {
                        const x = offsetX + j * blockSize;
                        const y = offsetY + i * blockSize;

                        this.nextCtx.fillRect(x, y, blockSize, blockSize);

                        // 添加边框
                        this.nextCtx.strokeStyle = '#000';
                        this.nextCtx.lineWidth = 1;
                        this.nextCtx.strokeRect(x, y, blockSize, blockSize);
                    }
                }
            }
        }
    }

    gameLoop() {
        if (!this.gameRunning) return;

        const currentTime = Date.now();

        if (!this.isPaused && currentTime - this.dropTime > this.dropInterval) {
            this.movePiece(0, 1);
            this.dropTime = currentTime;
        }

        this.draw();

        requestAnimationFrame(() => this.gameLoop());
    }
}

// 启动游戏
document.addEventListener('DOMContentLoaded', () => {
    new Tetris();
});
