<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>俄罗斯方块</h1>
        </div>
        
        <div class="game-content">
            <div class="game-info">
                <div class="score-section">
                    <h3>得分</h3>
                    <div id="score">0</div>
                </div>
                
                <div class="level-section">
                    <h3>等级</h3>
                    <div id="level">1</div>
                </div>
                
                <div class="lines-section">
                    <h3>消除行数</h3>
                    <div id="lines">0</div>
                </div>
                
                <div class="next-piece-section">
                    <h3>下一个</h3>
                    <canvas id="nextCanvas" width="120" height="120"></canvas>
                </div>
                
                <div class="controls-section">
                    <h3>控制说明</h3>
                    <div class="controls">
                        <div>← → : 左右移动</div>
                        <div>↓ : 快速下降</div>
                        <div>↑ : 旋转</div>
                        <div>空格 : 硬降落</div>
                        <div>P : 暂停/继续</div>
                    </div>
                </div>
            </div>
            
            <div class="game-board">
                <canvas id="gameCanvas" width="300" height="600"></canvas>
                <div id="gameOver" class="game-over hidden">
                    <h2>游戏结束</h2>
                    <p>最终得分: <span id="finalScore">0</span></p>
                    <button id="restartBtn">重新开始</button>
                </div>
                <div id="pauseOverlay" class="pause-overlay hidden">
                    <h2>游戏暂停</h2>
                    <p>按空格键继续</p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="tetris.js"></script>
</body>
</html>
